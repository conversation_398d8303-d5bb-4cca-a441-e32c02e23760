{
    'name': 'Product Cigarette & Tobacco Classification',
    'version': '********.0',
    'category': 'Sales',
    'summary': 'Add premium cigar, cigarette and tobacco classification to products with custom sorting',
    'description': """
Product Cigarette & Tobacco Classification
==========================================

This module adds:
* Boolean fields to classify products as premium cigars, cigarettes or tobacco
* Custom units of measure (cases/boxes for premium cigars, sticks for cigarettes, ml for tobacco)
* Sorted product lines in sales orders (premium cigars first, then cigarettes, then tobacco, then non tobacco)
* Enhanced product form view with classification fields

Features:
---------
* Premium cigar products are measured in cases/boxes (American standard)
* Cigarette products are measured in sticks
* Tobacco products are measured in ml
* Invoice and Sales Order PDF reports show products grouped by type (premium cigars first, then tobacco, then non tobacco)
* Easy product classification in product form
* No changes to sales order views - only affects PDF reports (invoice and sales order/quotation)
    """,
    'author': 'Arihantai',
    'website': 'https://www.arihantai.com/',
    'depends': [
        'base',
        'product',
        'sale',
        'account',
        'uom',
        # Add EDI dependencies if needed
        # 'base_edi',  # Uncomment when EDI modules are installed
    ],
    'data': [
        'data/uom_data.xml',
        'views/product_views.xml',
        'reports/invoice_report.xml',
        'reports/sale_report.xml',
    ],
    
    'installable': True,
    'auto_install': False,
    'application': False,
}
